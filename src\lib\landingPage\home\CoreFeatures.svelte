<script>
    import { Button } from '$lib/ui';
    import { SectionWrapper } from '$lib/landingPage';
    
    export let sectionLabel = "Core Features";
    export let headline = "Everything you need to get your 1600";
    export let subheadline = "Master the Digital SAT with our comprehensive platform designed by a perfect scorer.";
    export let features = [
        { icon: '<img src="/question-bank-icon.svg" alt="Question Bank" width="32" height="32" />', title: "Question Bank Practice", description: "Practice with thousands of high-quality questions organized by topic and difficulty, with detailed explanations for every answer." },
        { icon: '<img src="/simulation-icon.svg" alt="Simulations" width="32" height="32" />', title: "Simulations", description: "Take realistic Digital SAT simulations that adapt to your performance, just like the real test. Get instant scoring and detailed breakdowns." },
        { icon: '<img src="/dashboard-icon.svg" alt="Performance Analysis" width="32" height="32" />', title: "Performance Analysis", description: "See exactly where you stand with in-depth score predictions, skill breakdowns, and personalized study recommendations based on your results." },
        { icon: '<img src="/dashboard-icon.svg" alt="Dashboard" width="32" height="32" />', title: "Dashboard", description: "Track your progress, set goals, and stay motivated with our dashboard." },
        { icon: '<img src="/vocab-tool-icon.svg" alt="AI Vocabulary Tool" width="32" height="32" />', title: "AI Vocabulary Tool", description: "Build your vocabulary with state-of-the-art spaced repetition algorithm that helps you learn more words in less time." },
    ];
    export let primaryButton = "Get Started";
    export let secondaryButton = "All Features";
</script>

<SectionWrapper --bg-color="var(--purple)" --padding-top="8rem" --padding-bottom="8rem">
<div class="features-section">
    <div class="features-header">
        <p>{sectionLabel}</p>
        <h2>{@html headline}</h2>
        <p>{subheadline}</p>
    </div>
    
    <div class="features-grid">
        {#each features as feature}
            <div class="feature-card">
                <div class="feature-icon">{@html feature.icon}</div>
                <div class="feature-content">
                    <h3>{feature.title}</h3>
                    <p>{feature.description}</p>
                    <div class="feature-button">
                        <Button isSecondary>Learn More</Button>
                    </div>
                </div>
            </div>
        {/each}
    </div>
    
    <div class="features-buttons">
        <Button>{primaryButton}</Button>
        <Button isSecondary>{secondaryButton}</Button>
    </div>
</div>
</SectionWrapper>

<style>
    /* Features Section */
    .features-section {
        max-width: 1400px;
        width: 100%;
        text-align: center;
        position: relative;
    }

    .features-section::before {
        content: '';
        position: absolute;
        top: -2rem;
        left: -2rem;
        right: -2rem;
        bottom: -2rem;
        background: repeating-linear-gradient(
            45deg,
            transparent,
            transparent 10px,
            rgba(0, 0, 0, 0.03) 10px,
            rgba(0, 0, 0, 0.03) 20px
        );
        z-index: -1;
        pointer-events: none;
    }

    .features-header {
        margin-bottom: 5rem;
        position: relative;
        padding: 2rem;
        background: var(--white);
        border: 4px solid var(--pitch-black);
        box-shadow: 8px 8px 0 var(--pitch-black);
        transform: rotate(-0.5deg);
    }

    .features-header h2 {
        font-family: 'Inter', sans-serif;
        font-weight: 900;
        font-size: clamp(2rem, 5vw, 4rem);
        text-transform: uppercase;
        letter-spacing: -0.03em;
        line-height: 0.9;
        margin: 1rem 0;
        text-shadow: 4px 4px 0 var(--yellow);
    }

    .features-header p {
        font-family: 'Inter', sans-serif;
        font-weight: 600;
        font-size: 1.2rem;
        margin: 0;
        text-transform: uppercase;
        letter-spacing: 0.1em;
    }
    
    .features-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 3rem;
        margin-bottom: 4rem;
    }

    .feature-card {
        background: var(--white);
        border: 6px solid var(--pitch-black);
        padding: 0;
        text-align: left;
        display: flex;
        flex-direction: column;
        position: relative;
        box-shadow: 12px 12px 0 var(--pitch-black);
        transform: rotate(-1deg);
    }

    .feature-card:nth-child(even) {
        transform: rotate(1deg);
        background: var(--yellow);
    }

    .feature-card:nth-child(3n) {
        transform: rotate(-2deg);
        background: var(--sky-blue);
    }

    .feature-card:hover {
        transform: rotate(0deg) scale(1.02);
        box-shadow: 16px 16px 0 var(--pitch-black);
        z-index: 10;
    }

    .feature-icon {
        background: var(--pitch-black);
        width: 100%;
        height: 5rem;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        padding: 0 2rem;
        border-bottom: 6px solid var(--pitch-black);
        position: relative;
    }

    .feature-icon::after {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 0;
        height: 0;
        border-left: 30px solid var(--pitch-black);
        border-top: 30px solid transparent;
        border-bottom: 30px solid transparent;
    }

    .feature-icon :global(img) {
        width: 2.5rem;
        height: 2.5rem;
        filter: brightness(0) invert(1);
    }

    .feature-content {
        padding: 2rem;
        display: flex;
        flex-direction: column;
        gap: 1rem;
        flex-grow: 1;
    }

    .feature-content h3 {
        font-family: 'Inter', monospace;
        font-weight: 900;
        font-size: 1.5rem;
        text-transform: uppercase;
        letter-spacing: -0.02em;
        line-height: 1.1;
        margin: 0;
    }

    .feature-content p {
        font-family: 'Inter', sans-serif;
        font-weight: 500;
        font-size: 1rem;
        line-height: 1.4;
        margin: 0;
    }

    .feature-button {
        margin-top: auto;
        align-self: flex-start;
    }
    
    .features-buttons {
        display: flex;
        gap: 2rem;
        justify-content: center;
        flex-wrap: wrap;
        margin-top: 3rem;
        position: relative;
    }

    .features-buttons::before {
        content: '';
        position: absolute;
        top: -1rem;
        left: 50%;
        transform: translateX(-50%);
        width: 200px;
        height: 6px;
        background: var(--pitch-black);
        box-shadow: 0 6px 0 var(--yellow);
    }

    /* Mobile Responsiveness */
    @media (max-width: 768px) {
        .features-grid {
            grid-template-columns: 1fr;
            gap: 2rem;
        }

        .feature-card {
            transform: rotate(0deg);
        }

        .feature-card:nth-child(even),
        .feature-card:nth-child(3n) {
            transform: rotate(0deg);
        }

        .features-header {
            transform: rotate(0deg);
            margin-bottom: 3rem;
        }

        .features-header h2 {
            font-size: 2.5rem;
            text-shadow: 2px 2px 0 var(--yellow);
        }

        .feature-icon {
            height: 4rem;
        }

        .feature-content {
            padding: 1.5rem;
        }

        .features-buttons {
            gap: 1rem;
            margin-top: 2rem;
        }

        .features-buttons::before {
            width: 150px;
            height: 4px;
        }
    }
</style>
