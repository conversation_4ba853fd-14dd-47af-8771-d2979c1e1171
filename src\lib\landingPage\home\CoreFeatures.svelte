<script>
    import { H2, H3, P1, P2, <PERSON><PERSON> } from '$lib/ui';
    import { SectionWrapper } from '$lib/landingPage';
    
    export let sectionLabel = "Core Features";
    export let headline = "Everything you need to get your 1600";
    export let subheadline = "Master the Digital SAT with our comprehensive platform designed by a perfect scorer.";
    export let features = [
        { icon: '<img src="/question-bank-icon.svg" alt="Question Bank" width="32" height="32" />', title: "Question Bank Practice", description: "Practice with thousands of high-quality questions organized by topic and difficulty, with detailed explanations for every answer." },
        { icon: '<img src="/simulation-icon.svg" alt="Simulations" width="32" height="32" />', title: "Simulations", description: "Take realistic Digital SAT simulations that adapt to your performance, just like the real test. Get instant scoring and detailed breakdowns." },
        { icon: '<img src="/dashboard-icon.svg" alt="Performance Analysis" width="32" height="32" />', title: "Performance Analysis", description: "See exactly where you stand with in-depth score predictions, skill breakdowns, and personalized study recommendations based on your results." },
        { icon: '<img src="/dashboard-icon.svg" alt="Dashboard" width="32" height="32" />', title: "Dashboard", description: "Track your progress, set goals, and stay motivated with our dashboard." },
        { icon: '<img src="/vocab-tool-icon.svg" alt="AI Vocabulary Tool" width="32" height="32" />', title: "AI Vocabulary Tool", description: "Build your vocabulary with state-of-the-art spaced repetition algorithm that helps you learn more words in less time." },
    ];
    export let primaryButton = "Get Started";
    export let secondaryButton = "All Features";
</script>

<SectionWrapper --bg-color="var(--purple)" --padding-top="8rem" --padding-bottom="8rem">
<div class="features-section">
    <div class="features-header">
        <P2>{sectionLabel}</P2>
        <H2>{@html headline}</H2>
        <P1>{subheadline}</P1>
    </div>
    
    <div class="features-grid">
        {#each features as feature}
            <div class="feature-card">
                <div class="feature-icon">{@html feature.icon}</div>
                <H3>{feature.title}</H3>
                <P2>{feature.description}</P2>
                <Button isSecondary>Learn More</Button>
            </div>
        {/each}
    </div>
    
    <div class="features-buttons">
        <Button>{primaryButton}</Button>
        <Button isSecondary>{secondaryButton}</Button>
    </div>
</div>
</SectionWrapper>

<style>
    /* Features Section */
    .features-section {
        max-width: 1200px;
        width: 100%;
        text-align: center;
    }
    
    .features-header {
        margin-bottom: 4rem;
    }
    
    .features-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
        margin-bottom: 3rem;
    }
    
    .feature-card {
        background: white;
        border: 4px solid var(--pitch-black);
        padding: 2rem;
        text-align: center;
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
        align-items: center;
    }
    
    .feature-card:hover {
        transform: translate(-4px, -4px);
        box-shadow: 8px 8px 0 var(--pitch-black);
    }
    
    .feature-icon {
        background: var(--yellow);
        width: 4rem;
        height: 4rem;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 3px solid var(--pitch-black);
    }

    .feature-icon :global(img) {
        width: 2rem;
        height: 2rem;
        filter: brightness(0);
    }
    
    .features-buttons {
        display: flex;
        gap: 1rem;
        justify-content: center;
        flex-wrap: wrap;
    }
    
    /* Mobile Responsiveness */
    @media (max-width: 768px) {
        .features-grid {
            grid-template-columns: 1fr;
        }
    }
</style>
